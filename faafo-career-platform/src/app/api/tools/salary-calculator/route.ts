import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { z } from 'zod';
import { withRateLimit } from '@/lib/rateLimit';
import { with<PERSON><PERSON><PERSON><PERSON>and<PERSON> } from '@/lib/errorHandler';
import { SecurityMiddleware } from '@/lib/security-middleware';
import {
  SalaryCalculatorForm,
  SalaryCalculationResult,
  SalaryCalculationResponse,
  CareerPathData,
  LocationData,
  ExperienceData,
  CompanySizeData,
  EducationData,
  EXPERIENCE_LEVELS,
  EDUCATION_LEVELS,
  COMPANY_SIZES,
  SALARY_CALCULATOR_CONFIG
} from '@/types/salary-calculator';

// Enhanced validation schema with input sanitization
const salaryCalculationSchema = z.object({
  careerPath: z.string()
    .min(1, 'Career path is required')
    .max(100, 'Career path too long')
    .regex(/^[a-zA-Z0-9\s\/\-\+\.]+$/, 'Career path contains invalid characters'),
  experienceLevel: z.enum(EXPERIENCE_LEVELS),
  location: z.string()
    .min(1, 'Location is required')
    .max(100, 'Location too long')
    .regex(/^[a-zA-Z0-9\s,\.\-]+$/, 'Location contains invalid characters'),
  skills: z.array(
    z.string()
      .min(1, 'Skill cannot be empty')
      .max(50, 'Skill name too long')
      .regex(/^[a-zA-Z0-9\s\.\-\+\#]+$/, 'Skill contains invalid characters')
  ).max(20, 'Maximum 20 skills allowed').optional().default([]),
  education: z.enum(EDUCATION_LEVELS).optional().default('bachelor'),
  companySize: z.enum(COMPANY_SIZES).optional().default('medium'),
  industry: z.string()
    .max(100, 'Industry must be less than 100 characters')
    .regex(/^[a-zA-Z0-9\s\-\&\.]+$/, 'Industry contains invalid characters')
    .optional().default('technology'),
}).transform((data) => ({
  // Additional sanitization
  careerPath: data.careerPath.trim(),
  experienceLevel: data.experienceLevel,
  location: data.location.trim(),
  skills: data.skills.map(skill => skill.trim()).filter(skill => skill.length > 0),
  education: data.education,
  companySize: data.companySize,
  industry: data.industry.trim(),
}));

// Enhanced salary data matching all career paths in the database
const SALARY_DATABASE: Record<string, CareerPathData> = {
  'AI/Machine Learning Engineer': {
    min: 95000, max: 200000, growth: '22.1%', demand: 'high',
    skills: ['Python', 'TensorFlow', 'PyTorch', 'Machine Learning', 'Deep Learning']
  },
  'Cloud Engineer / DevOps Specialist': {
    min: 85000, max: 165000, growth: '12.7%', demand: 'high',
    skills: ['AWS', 'Azure', 'Docker', 'Kubernetes', 'CI/CD']
  },
  'Cloud Solutions Architect': {
    min: 110000, max: 220000, growth: '15.3%', demand: 'high',
    skills: ['AWS', 'Azure', 'GCP', 'Architecture Design', 'Enterprise Solutions']
  },
  'Cybersecurity Specialist': {
    min: 75000, max: 140000, growth: '18.4%', demand: 'high',
    skills: ['Network Security', 'Incident Response', 'Risk Assessment', 'SIEM', 'Compliance']
  },
  'Data Scientist': {
    min: 80000, max: 160000, growth: '11.5%', demand: 'high',
    skills: ['Python', 'R', 'SQL', 'Machine Learning', 'Statistics']
  },
  'DevOps Engineer': {
    min: 85000, max: 165000, growth: '12.7%', demand: 'high',
    skills: ['AWS', 'Docker', 'Kubernetes', 'CI/CD', 'Infrastructure as Code']
  },
  'Digital Marketing Specialist': {
    min: 50000, max: 100000, growth: '4.3%', demand: 'medium',
    skills: ['SEO', 'Google Analytics', 'Social Media', 'Content Marketing', 'PPC']
  },
  'Freelance Web Developer': {
    min: 40000, max: 120000, growth: '8.1%', demand: 'high',
    skills: ['HTML', 'CSS', 'JavaScript', 'React', 'WordPress']
  },
  'Product Manager': {
    min: 90000, max: 180000, growth: '6.2%', demand: 'high',
    skills: ['Strategy', 'Analytics', 'Communication', 'Agile', 'User Research']
  },
  'Simple Online Business Owner': {
    min: 30000, max: 200000, growth: '15.0%', demand: 'medium',
    skills: ['E-commerce', 'Digital Marketing', 'Customer Service', 'Analytics', 'Business Strategy']
  },
  'UX/UI Designer': {
    min: 65000, max: 130000, growth: '5.8%', demand: 'medium',
    skills: ['Figma', 'User Research', 'Prototyping', 'Design Systems', 'Usability Testing']
  },
};

// Location cost of living multipliers with more cities
const LOCATION_MULTIPLIERS: Record<string, LocationData> = {
  'San Francisco, CA': { multiplier: 1.8, impact: 'Very High Cost', costOfLiving: 180, marketSize: 'Large' },
  'New York, NY': { multiplier: 1.6, impact: 'Very High Cost', costOfLiving: 160, marketSize: 'Large' },
  'Seattle, WA': { multiplier: 1.4, impact: 'High Cost', costOfLiving: 140, marketSize: 'Large' },
  'Los Angeles, CA': { multiplier: 1.3, impact: 'High Cost', costOfLiving: 130, marketSize: 'Large' },
  'Boston, MA': { multiplier: 1.3, impact: 'High Cost', costOfLiving: 130, marketSize: 'Medium' },
  'Washington, DC': { multiplier: 1.2, impact: 'High Cost', costOfLiving: 120, marketSize: 'Medium' },
  'Chicago, IL': { multiplier: 1.1, impact: 'Medium Cost', costOfLiving: 110, marketSize: 'Medium' },
  'Austin, TX': { multiplier: 1.0, impact: 'Medium Cost', costOfLiving: 100, marketSize: 'Medium' },
  'Denver, CO': { multiplier: 1.0, impact: 'Medium Cost', costOfLiving: 100, marketSize: 'Medium' },
  'Atlanta, GA': { multiplier: 0.9, impact: 'Low Cost', costOfLiving: 90, marketSize: 'Medium' },
  'Phoenix, AZ': { multiplier: 0.9, impact: 'Low Cost', costOfLiving: 90, marketSize: 'Small' },
  'Dallas, TX': { multiplier: 0.9, impact: 'Low Cost', costOfLiving: 90, marketSize: 'Medium' },
  'Remote': { multiplier: 0.95, impact: 'Remote Work', costOfLiving: 95, marketSize: 'Global' },
  'Other': { multiplier: 0.85, impact: 'Other Location', costOfLiving: 85, marketSize: 'Small' },
};

// Experience level multipliers with detailed progression
const EXPERIENCE_MULTIPLIERS: Record<string, ExperienceData> = {
  'entry': { multiplier: 0.7, impact: 'Entry Level', description: '0-1 years experience' },
  'junior': { multiplier: 0.85, impact: 'Junior Level', description: '1-3 years experience' },
  'mid': { multiplier: 1.0, impact: 'Mid Level', description: '3-5 years experience' },
  'senior': { multiplier: 1.3, impact: 'Senior Level', description: '5-8 years experience' },
  'lead': { multiplier: 1.6, impact: 'Lead Level', description: '8-12 years experience' },
  'principal': { multiplier: 2.0, impact: 'Principal Level', description: '12+ years experience' },
  'executive': { multiplier: 2.5, impact: 'Executive Level', description: 'Executive level' },
};

// Using the unified interface from types file

function calculateSalary(data: z.infer<typeof salaryCalculationSchema>): SalaryCalculationResult {
  const careerData = SALARY_DATABASE[data.careerPath];
  if (!careerData) {
    throw new Error('Career path not found in database');
  }

  const locationData = LOCATION_MULTIPLIERS[data.location] || LOCATION_MULTIPLIERS['Other'];
  const experienceData = EXPERIENCE_MULTIPLIERS[data.experienceLevel];

  // Calculate multipliers
  const locationMultiplier = locationData.multiplier;
  const experienceMultiplier = experienceData.multiplier;
  
  // Skills bonus calculation
  const relevantSkills = data.skills.filter(skill => 
    careerData.skills.some(careerSkill => 
      careerSkill.toLowerCase().includes(skill.toLowerCase()) ||
      skill.toLowerCase().includes(careerSkill.toLowerCase())
    )
  );
  const skillsBonus = Math.min(relevantSkills.length * 0.05, 0.25); // Max 25% bonus
  
  // Education multiplier
  const educationMultipliers: Record<string, EducationData> = {
    'high_school': { multiplier: 0.9, impact: 'Below Average', description: 'High school diploma' },
    'associate': { multiplier: 0.95, impact: 'Slightly Below Average', description: 'Associate degree' },
    'bachelor': { multiplier: 1.0, impact: 'Average', description: 'Bachelor\'s degree' },
    'master': { multiplier: 1.15, impact: 'Above Average', description: 'Master\'s degree' },
    'phd': { multiplier: 1.3, impact: 'Significantly Above Average', description: 'Doctoral degree' },
    'bootcamp': { multiplier: 0.95, impact: 'Slightly Below Average', description: 'Coding bootcamp' },
    'self_taught': { multiplier: 0.9, impact: 'Below Average', description: 'Self-taught skills' },
  };
  const educationData = educationMultipliers[data.education];

  // Company size multiplier
  const companySizeMultipliers: Record<string, CompanySizeData> = {
    'startup': { multiplier: 0.9, impact: 'Lower Base, Higher Equity', description: 'Startup company (1-50 employees)' },
    'small': { multiplier: 0.95, impact: 'Slightly Lower', description: 'Small company (51-200 employees)' },
    'medium': { multiplier: 1.0, impact: 'Average', description: 'Medium company (201-1000 employees)' },
    'large': { multiplier: 1.1, impact: 'Above Average', description: 'Large company (1001-10000 employees)' },
    'enterprise': { multiplier: 1.2, impact: 'Significantly Higher', description: 'Enterprise company (10000+ employees)' },
  };
  const companySizeData = companySizeMultipliers[data.companySize];

  // Enhanced salary calculation with market adjustments
  const baseMultiplier = locationMultiplier * experienceMultiplier * educationData.multiplier * companySizeData.multiplier;
  const skillsMultiplier = 1 + skillsBonus;

  // Apply demand-based market adjustment
  const demandMultiplier = careerData.demand === 'high' ? 1.05 :
                          careerData.demand === 'medium' ? 1.0 : 0.95;

  // Calculate total multiplier with market factors
  const totalMultiplier = baseMultiplier * skillsMultiplier * demandMultiplier;

  // Apply multiplier to base range
  let adjustedMin = Math.round(careerData.min * totalMultiplier);
  let adjustedMax = Math.round(careerData.max * totalMultiplier);

  // Ensure minimum spread and realistic bounds
  const minSpread = adjustedMin * 0.2; // At least 20% spread
  if (adjustedMax - adjustedMin < minSpread) {
    adjustedMax = adjustedMin + minSpread;
  }

  // Apply realistic bounds (no salary below $25k or above $500k for most roles)
  adjustedMin = Math.max(adjustedMin, 25000);
  adjustedMax = Math.min(adjustedMax, 500000);

  const median = Math.round((adjustedMin + adjustedMax) / 2);

  // Enhanced confidence score calculation
  let confidence: number = SALARY_CALCULATOR_CONFIG.BASE_CONFIDENCE;

  // Career path confidence (30 points max)
  if (data.careerPath in SALARY_DATABASE) {
    confidence += 30;
  } else {
    confidence += 5; // Fallback case
  }

  // Location confidence (25 points max)
  if (data.location in LOCATION_MULTIPLIERS) {
    const locationData = LOCATION_MULTIPLIERS[data.location];
    confidence += locationData.marketSize === 'Large' ? 25 :
                  locationData.marketSize === 'Medium' ? 20 : 15;
  } else {
    confidence += 10; // Unknown location
  }

  // Skills confidence (20 points max)
  const skillsRatio = relevantSkills.length / Math.max(careerData.skills.length, 1);
  if (skillsRatio >= 0.7) {
    confidence += 20; // High skill match
  } else if (skillsRatio >= 0.4) {
    confidence += 15; // Medium skill match
  } else if (skillsRatio > 0) {
    confidence += 10; // Some skill match
  } else {
    confidence += 5; // No skill match
  }

  // Experience confidence (15 points max)
  if (data.experienceLevel) {
    confidence += 15;
  } else {
    confidence += 5;
  }

  // Education confidence (5 points max)
  if (data.education && data.education !== 'bachelor') {
    confidence += 5; // Non-standard education adds uncertainty
  } else {
    confidence += 5; // Standard education
  }

  // Cap at maximum confidence
  confidence = Math.min(confidence, 95);

  // Generate recommendations
  const recommendations: string[] = [];
  
  if (locationMultiplier < 1.0) {
    recommendations.push("Consider remote work or relocating to higher-paying markets");
  }
  
  if (skillsBonus < 0.15) {
    recommendations.push(`Develop key skills: ${careerData.skills.slice(0, 3).join(', ')}`);
  }
  
  if (experienceMultiplier < 1.0) {
    recommendations.push("Gain more experience or seek leadership opportunities");
  }
  
  if (confidence < 70) {
    recommendations.push("Research more specific salary data for your exact role and location");
  }
  
  recommendations.push("Negotiate based on total compensation, not just base salary");
  recommendations.push("Consider company equity, benefits, and growth opportunities");

  // Calculate realistic percentiles based on salary distribution
  // Use log-normal distribution approximation for salary data
  const logMedian = Math.log(median);
  const sigma = 0.3; // Standard deviation for salary distribution

  const percentile25 = Math.round(Math.exp(logMedian - 0.674 * sigma));
  const percentile75 = Math.round(Math.exp(logMedian + 0.674 * sigma));

  // National average without location adjustment
  const baseMedian = Math.round((careerData.min + careerData.max) / 2);
  const nationalAverage = Math.round(baseMedian * experienceMultiplier * (1 + skillsBonus * 0.5) * educationData.multiplier * demandMultiplier);

  return {
    baseRange: { min: careerData.min, max: careerData.max },
    adjustedRange: { min: adjustedMin, max: adjustedMax },
    median,
    factors: {
      location: {
        multiplier: locationMultiplier,
        impact: locationData.impact,
        description: `Cost of living adjustment for ${data.location}`
      },
      experience: {
        multiplier: experienceMultiplier,
        impact: experienceData.impact,
        description: experienceData.description
      },
      skills: {
        bonus: skillsBonus,
        impact: skillsBonus > 0.1 ? 'Strong' : skillsBonus > 0 ? 'Moderate' : 'Minimal',
        relevantSkills: relevantSkills,
        description: `${relevantSkills.length} relevant skills identified`
      },
      education: {
        multiplier: educationData.multiplier,
        impact: educationData.impact,
        description: educationData.description
      },
      companySize: {
        multiplier: companySizeData.multiplier,
        impact: companySizeData.impact,
        description: companySizeData.description
      },
    },
    confidence,
    dataPoints: SALARY_CALCULATOR_CONFIG.DEFAULT_DATA_POINTS,
    dataSource: 'Market Research 2024',
    marketInsights: {
      demand: careerData.demand,
      growth: careerData.growth,
      topSkills: careerData.skills,
      recommendations,
    },
    comparisons: {
      percentile25,
      percentile75,
      nationalAverage,
    },
    metadata: {
      calculatedAt: new Date().toISOString(),
      version: SALARY_CALCULATOR_CONFIG.VERSION,
      currency: SALARY_CALCULATOR_CONFIG.CURRENCY,
    },
  };
}

// POST endpoint for salary calculation with enhanced security
export const POST = withErrorHandler(async (request: NextRequest) => {
  return SecurityMiddleware.secureWithBodyValidation(
    request,
    async (body) => {
      const session = await getServerSession(authOptions);
      let validation: any = null;

      try {
        // Body is already parsed and validated by security middleware
        validation = salaryCalculationSchema.safeParse(body);

        if (!validation.success) {
          return NextResponse.json(
            {
              success: false,
              error: 'Invalid request data',
              details: validation.error.errors
            },
            { status: 400 }
          );
        }

        const result = calculateSalary(validation.data);

        // Enhanced logging with structured data
        if (session?.user?.id) {
          console.log(JSON.stringify({
            event: 'salary_calculation_success',
            userId: session.user.id,
            careerPath: validation.data.careerPath,
            location: validation.data.location,
            experienceLevel: validation.data.experienceLevel,
            skillsCount: validation.data.skills.length,
            confidence: result.confidence,
            timestamp: new Date().toISOString()
          }));
        } else {
          console.log(JSON.stringify({
            event: 'salary_calculation_success_anonymous',
            careerPath: validation.data.careerPath,
            location: validation.data.location,
            experienceLevel: validation.data.experienceLevel,
            skillsCount: validation.data.skills.length,
            confidence: result.confidence,
            timestamp: new Date().toISOString()
          }));
        }

        return NextResponse.json({
          success: true,
          data: result,
          message: 'Salary calculation completed successfully'
        });

      } catch (error) {
        // Enhanced error logging with context
        const errorDetails = {
          event: 'salary_calculation_error',
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          userId: session?.user?.id || 'anonymous',
          requestData: validation?.data || 'validation_failed',
          timestamp: new Date().toISOString()
        };

        console.error(JSON.stringify(errorDetails));

        // Return user-friendly error without exposing internals
        return NextResponse.json(
          {
            success: false,
            error: error instanceof Error && error.message.includes('Career path not found')
              ? 'Selected career path is not available'
              : 'Unable to calculate salary at this time. Please try again.'
          },
          { status: 500 }
        );
      }
    },
    {
      requireAuth: false, // Allow public access
      requireCSRF: true, // Re-enabled CSRF protection
      rateLimitType: 'api',
      maxBodySize: 2048 // 2KB should be enough for salary calculation data
    }
  );
});

// GET endpoint for salary data and metadata with security
export const GET = withErrorHandler(async (request: NextRequest) => {
  return SecurityMiddleware.secureRead(
    request,
    async () => {
      const { searchParams } = new URL(request.url);
      const type = searchParams.get('type');

      try {
        switch (type) {
          case 'career-paths':
            return NextResponse.json({
              success: true,
              data: Object.keys(SALARY_DATABASE).map(path => ({
                name: path,
                ...SALARY_DATABASE[path]
              }))
            });

          case 'locations':
            return NextResponse.json({
              success: true,
              data: Object.entries(LOCATION_MULTIPLIERS).map(([location, data]) => ({
                name: location,
                ...data
              }))
            });

          case 'experience-levels':
            return NextResponse.json({
              success: true,
              data: Object.entries(EXPERIENCE_MULTIPLIERS).map(([level, data]) => ({
                level,
                ...data
              }))
            });

          default:
            return NextResponse.json({
              success: true,
              data: {
                careerPaths: Object.keys(SALARY_DATABASE),
                locations: Object.keys(LOCATION_MULTIPLIERS),
                experienceLevels: Object.keys(EXPERIENCE_MULTIPLIERS),
                totalCareerPaths: Object.keys(SALARY_DATABASE).length,
                lastUpdated: new Date().toISOString(),
              }
            });
        }
      } catch (error) {
        // SECURITY FIX: Replace console.error with proper error monitoring
        // TODO: Implement Sentry or similar error tracking
        // console.error('Error fetching salary data:', error);

        return NextResponse.json(
          { success: false, error: 'Internal server error' },
          { status: 500 }
        );
      }
    },
    {
      rateLimitType: 'search' // Use search rate limit for GET requests
    }
  );
});
