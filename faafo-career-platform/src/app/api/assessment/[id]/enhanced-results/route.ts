import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { EnhancedAssessmentService } from '@/lib/enhancedAssessmentService';
import { AssessmentResponse } from '@/lib/assessmentScoring';
import { withCSRFProtection } from '@/lib/csrf';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id: assessmentId } = await params;

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!assessmentId) {
      return NextResponse.json(
        { success: false, error: 'Assessment ID is required' },
        { status: 400 }
      );
    }

    // Verify assessment belongs to user
    const assessment = await prisma.assessment.findFirst({
      where: {
        id: assessmentId,
        userId: session.user.id
      },
      include: {
        responses: true
      }
    });

    if (!assessment) {
      return NextResponse.json(
        { success: false, error: 'Assessment not found or access denied' },
        { status: 404 }
      );
    }

    if (assessment.status !== 'COMPLETED') {
      return NextResponse.json(
        { success: false, error: 'Assessment is not completed' },
        { status: 400 }
      );
    }

    // Convert assessment responses to the expected format
    const responseData: AssessmentResponse = {};
    assessment.responses.forEach(response => {
      try {
        const value = typeof response.answerValue === 'string'
          ? JSON.parse(response.answerValue)
          : response.answerValue;
        responseData[response.questionKey] = value as string | string[] | number | null;
      } catch {
        responseData[response.questionKey] = response.answerValue as string | string[] | number | null;
      }
    });

    // Generate enhanced results
    const enhancedResults = await EnhancedAssessmentService.generateEnhancedResults(
      assessmentId,
      responseData
    );

    // Log successful generation
    console.log(`Enhanced assessment results generated for user ${session.user.id}, assessment ${assessmentId}`);

    return NextResponse.json({
      success: true,
      data: enhancedResults
    });

  } catch (error) {
    console.error('Error generating enhanced assessment results:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate enhanced assessment results',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
}

// POST endpoint to regenerate results with updated preferences
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id: assessmentId } = await params;
    const body = await request.json();

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!assessmentId) {
      return NextResponse.json(
        { success: false, error: 'Assessment ID is required' },
        { status: 400 }
      );
    }

    // Verify assessment belongs to user
    const assessment = await prisma.assessment.findFirst({
      where: {
        id: assessmentId,
        userId: session.user.id
      },
      include: {
        responses: true
      }
    });

    if (!assessment) {
      return NextResponse.json(
        { success: false, error: 'Assessment not found or access denied' },
        { status: 404 }
      );
    }

    // Extract preferences from request body
    const {
      focusAreas = [],
      timeCommitment = 'MODERATE',
      budgetPreference = 'FREE_PREFERRED',
      learningStyle = 'MIXED'
    } = body;

    // Convert assessment responses to the expected format
    const responseData: AssessmentResponse = {};
    assessment.responses.forEach(response => {
      try {
        const value = typeof response.answerValue === 'string'
          ? JSON.parse(response.answerValue)
          : response.answerValue;
        responseData[response.questionKey] = value as string | string[] | number | null;
      } catch {
        responseData[response.questionKey] = response.answerValue as string | string[] | number | null;
      }
    });

    // Add user preferences to response data
    (responseData as any).user_preferences = {
      focusAreas,
      timeCommitment,
      budgetPreference,
      learningStyle
    };

    // Generate enhanced results with preferences
    const enhancedResults = await EnhancedAssessmentService.generateEnhancedResults(
      assessmentId,
      responseData
    );

    // Log successful regeneration
    console.log(`Enhanced assessment results regenerated with preferences for user ${session.user.id}, assessment ${assessmentId}`);

    return NextResponse.json({
      success: true,
      data: enhancedResults,
      message: 'Results updated with your preferences'
    });

  } catch (error) {
    console.error('Error regenerating enhanced assessment results:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to regenerate enhanced assessment results',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
}

// PATCH endpoint to save user feedback on recommendations
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id: assessmentId } = await params;
    const body = await request.json();

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const {
      careerPathFeedback = {},
      resourceFeedback = {},
      overallRating,
      comments
    } = body;

    // Save feedback to database (you might want to create a feedback table)
    // For now, we'll log it and return success
    console.log(`Assessment feedback received from user ${session.user.id}:`, {
      assessmentId,
      careerPathFeedback,
      resourceFeedback,
      overallRating,
      comments
    });

    // In a production system, you would save this feedback to improve recommendations
    // await prisma.assessmentFeedback.create({
    //   data: {
    //     assessmentId,
    //     userId: session.user.id,
    //     careerPathFeedback,
    //     resourceFeedback,
    //     overallRating,
    //     comments
    //   }
    // });

    return NextResponse.json({
      success: true,
      message: 'Thank you for your feedback! This helps us improve our recommendations.'
    });

  } catch (error) {
    console.error('Error saving assessment feedback:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to save feedback'
      },
      { status: 500 }
    );
  }
}
