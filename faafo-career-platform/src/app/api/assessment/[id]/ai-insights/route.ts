import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import prisma from '@/lib/prisma';
import { aiEnhancedAssessmentService } from '@/lib/aiEnhancedAssessmentService';
import { EnhancedAssessmentService } from '@/lib/enhancedAssessmentService';
import { AssessmentResponse } from '@/lib/assessmentScoring';
import { cache } from '@/lib/cache';
import { withRateLimit, rateLimitConfigs, sanitizeInput } from '@/lib/rateLimit';
import { z } from 'zod';
import { withCSRFProtection } from '@/lib/csrf';

// Input validation schema
const aiInsightsRequestSchema = z.object({
  assessmentId: z.string().uuid('Invalid assessment ID format'),
  focusAreas: z.array(z.string()).optional(),
  analysisDepth: z.enum(['basic', 'standard', 'comprehensive']).optional(),
  includeMarketData: z.boolean().optional(),
  personalityFocus: z.boolean().optional(),
});

// Rate limit configuration for AI insights
const aiInsightsRateLimit = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5, // 5 AI insights generations per 15 minutes
  message: 'Too many AI insights requests. Please wait before generating more insights.',
};

interface AIInsightsResponse {
  insights: any;
  cached: boolean;
  generatedAt: string;
}

export const GET = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<AIInsightsResponse>>> => {
  return withRateLimit(request, aiInsightsRateLimit, async () => {
    const session = await getServerSession(authOptions);
    const { id: assessmentId } = await params;

    // Authentication check
    if (!session?.user?.id) {
      const error = new Error('Authentication required') as any;
      error.statusCode = 401;
      throw error;
    }

    // Input validation
    try {
      aiInsightsRequestSchema.parse({ assessmentId });
    } catch (validationError) {
      const error = new Error('Invalid request parameters') as any;
      error.statusCode = 400;
      error.details = validationError instanceof z.ZodError ? validationError.errors : undefined;
      throw error;
    }

    // Check if AI services are available
    if (!process.env.GOOGLE_GEMINI_API_KEY) {
      const error = new Error('AI services are temporarily unavailable') as any;
      error.statusCode = 503;
      error.fallback = true;
      error.retryAfter = 300; // 5 minutes
      throw error;
    }

      // Check cache first with improved error handling
      const cacheKey = `ai_insights:${assessmentId}:${session.user.id}:v1.0.0`;

      try {
        const cachedInsights = await cache.get(cacheKey);

        if (cachedInsights && typeof cachedInsights === 'string') {
          const parsed = JSON.parse(cachedInsights);

          // Validate cached data structure
          if (parsed && typeof parsed === 'object' && parsed.personalityAnalysis) {
            return NextResponse.json({
              success: true,
              data: parsed,
              cached: true,
              message: 'AI insights retrieved from cache',
              generatedAt: parsed.generatedAt || new Date().toISOString()
            });
          }
        }
      } catch (cacheError) {
        console.error('Cache retrieval error:', cacheError);
        // Continue to generate new insights if cache fails
      }

      // Verify assessment belongs to user with improved error handling
      const assessment = await prisma.assessment.findFirst({
        where: {
          id: assessmentId,
          userId: session.user.id
        },
        include: {
          responses: true,
          user: {
            select: {
              id: true,
              email: true,
              name: true
            }
          }
        }
      });

    if (!assessment) {
      const error = new Error('Assessment not found or access denied') as any;
      error.statusCode = 404;
      error.code = 'ASSESSMENT_NOT_FOUND';
      throw error;
    }

    // Validate assessment has sufficient data
    if (!assessment.responses || assessment.responses.length < 3) {
      const error = new Error('Insufficient assessment data for AI analysis') as any;
      error.statusCode = 400;
      error.code = 'INSUFFICIENT_DATA';
      error.requiredResponses = 3;
      error.currentResponses = assessment.responses?.length || 0;
      throw error;
    }

    if (assessment.status !== 'COMPLETED') {
      const error = new Error('Assessment is not completed') as any;
      error.statusCode = 400;
      throw error;
    }

    // Convert assessment responses to the expected format
    const responseData: AssessmentResponse = {};
    assessment.responses.forEach(response => {
      try {
        const value = typeof response.answerValue === 'string' 
          ? JSON.parse(response.answerValue) 
          : response.answerValue;
        responseData[response.questionKey] = value as string | string[] | number | null;
      } catch {
        responseData[response.questionKey] = response.answerValue as string | string[] | number | null;
      }
    });

    // Generate enhanced results first
    const enhancedResults = await EnhancedAssessmentService.generateEnhancedResults(
      assessmentId,
      responseData
    );

      // Generate AI insights with timeout and retry logic
      console.log(`Generating AI insights for user ${session.user.id}, assessment ${assessmentId}`);

      const startTime = Date.now();
      let aiInsights;

      try {
        // Set timeout for AI generation (5 minutes)
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('AI generation timeout')), 5 * 60 * 1000);
        });

        const aiGenerationPromise = aiEnhancedAssessmentService.generateAIInsights(
          assessmentId,
          responseData,
          enhancedResults.insights,
          enhancedResults.careerPathRecommendations,
          session.user.id
        );

        aiInsights = await Promise.race([aiGenerationPromise, timeoutPromise]);

      } catch (aiError) {
        console.error('AI insights generation error:', aiError);

        // Return fallback insights with error information
        return NextResponse.json(
          {
            success: false,
            error: 'AI insights generation failed',
            code: 'AI_GENERATION_FAILED',
            fallback: true,
            retryAfter: 60, // 1 minute
            details: aiError instanceof Error ? aiError.message : 'Unknown error'
          },
          { status: 500 }
        );
      }

      const generationTime = Date.now() - startTime;
      console.log(`AI insights generated in ${generationTime}ms for user ${session.user.id}, assessment ${assessmentId}`);

      // Cache the results for 24 hours with error handling
      try {
        await cache.set(cacheKey, JSON.stringify(aiInsights), 86400);
      } catch (cacheError) {
        console.error('Failed to cache AI insights:', cacheError);
        // Continue without caching
      }

    return NextResponse.json({
      success: true,
      data: {
        insights: aiInsights,
        cached: false,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error in AI insights generation:', error);
    throw error; // Let unified error handler manage this
  }
});

interface AIInsightsRegenerateResponse {
  insights: any;
  regenerated: boolean;
  focusAreas: string[];
  generatedAt: string;
}

// POST endpoint to regenerate AI insights with specific focus areas
export const POST = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<AIInsightsRegenerateResponse>>> => {
  const session = await getServerSession(authOptions);
  const { id: assessmentId } = await params;
  const body = await request.json();

  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
    }

    if (!process.env.GOOGLE_GEMINI_API_KEY) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'AI services are not configured' 
        },
        { status: 503 }
      );
    }

    const {
      focusAreas = [],
      analysisDepth = 'standard',
      includeMarketData = true,
      personalityFocus = true
    } = body;

    // Verify assessment belongs to user
    const assessment = await prisma.assessment.findFirst({
      where: {
        id: assessmentId,
        userId: session.user.id
      },
      include: {
        responses: true
      }
    });

    if (!assessment) {
      return NextResponse.json(
        { success: false, error: 'Assessment not found or access denied' },
        { status: 404 }
      );
    }

    // Convert assessment responses
    const responseData: AssessmentResponse = {};
    assessment.responses.forEach(response => {
      try {
        const value = typeof response.answerValue === 'string' 
          ? JSON.parse(response.answerValue) 
          : response.answerValue;
        responseData[response.questionKey] = value as string | string[] | number | null;
      } catch {
        responseData[response.questionKey] = response.answerValue as string | string[] | number | null;
      }
    });

    // Add user preferences to response data for AI analysis
    (responseData as any).ai_preferences = {
      focusAreas,
      analysisDepth,
      includeMarketData,
      personalityFocus
    };

    // Generate enhanced results with preferences
    const enhancedResults = await EnhancedAssessmentService.generateEnhancedResults(
      assessmentId,
      responseData
    );

    // Generate AI insights with custom focus
    const aiInsights = await aiEnhancedAssessmentService.generateAIInsights(
      assessmentId,
      responseData,
      enhancedResults.insights,
      enhancedResults.careerPathRecommendations,
      session.user.id
    );

    // Cache with custom key for preferences
    const customCacheKey = `ai_insights:${assessmentId}:${session.user.id}:custom:${Date.now()}`;
    await cache.set(customCacheKey, JSON.stringify(aiInsights), 3600); // 1 hour cache

    console.log(`Custom AI insights generated for user ${session.user.id}, assessment ${assessmentId}`);

  return NextResponse.json({
    success: true,
    data: {
      insights: aiInsights,
      regenerated: true,
      focusAreas,
      generatedAt: new Date().toISOString()
    }
  });

} catch (error) {
  console.error('Error generating custom AI insights:', error);
  throw error; // Let unified error handler manage this
}
}

interface CacheClearResponse {
  cleared: boolean;
  assessmentId: string;
  clearedAt: string;
}

// DELETE endpoint to clear AI insights cache
export const DELETE = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<CacheClearResponse>>> => {
  const session = await getServerSession(authOptions);
  const { id: assessmentId } = await params;

  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

    // Clear cache for this assessment
    const cacheKey = `ai_insights:${assessmentId}:${session.user.id}`;
    cache.delete(cacheKey);

    console.log(`AI insights cache cleared for user ${session.user.id}, assessment ${assessmentId}`);

  return NextResponse.json({
    success: true,
    data: {
      cleared: true,
      assessmentId,
      clearedAt: new Date().toISOString()
    }
  });

} catch (error) {
  console.error('Error clearing AI insights cache:', error);
  throw error; // Let unified error handler manage this
}
}
