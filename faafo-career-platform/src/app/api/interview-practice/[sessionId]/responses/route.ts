import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { withErrorHandler } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { withSecureErrorHandling } from '@/lib/secure-error-handler';
import BusinessLogicSecurity from '@/lib/business-logic-security';
import { UserValidationService } from '@/lib/user-validation-service';
import { SelfHealingAIService } from '@/lib/self-healing-ai-service';
import { UnifiedValidationService } from '@/lib/unified-validation-service';
import { z } from 'zod';
import { withCSRFProtection } from '@/lib/csrf';

// Validation schema for submitting responses
const submitResponseSchema = z.object({
  questionId: z.string().uuid(),
  responseText: z.string().min(10, 'Response must be at least 10 characters').max(5000, 'Response too long'),
  audioUrl: z.string().url().optional().nullable().transform(val => val || undefined),
  videoUrl: z.string().url().optional().nullable().transform(val => val || undefined),
  responseTime: z.number().min(0).max(3600), // Max 1 hour
  preparationTime: z.number().min(0).max(1800).default(0), // Max 30 minutes
  userNotes: z.string().max(1000).optional().nullable().transform(val => val || undefined),
  requestFeedback: z.boolean().default(true),
});

// Validation schema for updating responses
const updateResponseSchema = z.object({
  responseText: z.string().min(10).max(5000).optional(),
  audioUrl: z.string().url().optional().nullable().transform(val => val || undefined),
  videoUrl: z.string().url().optional().nullable().transform(val => val || undefined),
  responseTime: z.number().min(0).max(3600).optional(),
  preparationTime: z.number().min(0).max(1800).optional(),
  userNotes: z.string().max(1000).optional().nullable().transform(val => val || undefined),
  needsReview: z.boolean().optional(),
});

// GET - Retrieve responses for a session
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) {
  return withRateLimit(
    request,
    {
      windowMs: 15 * 60 * 1000,
      maxRequests: process.env.NODE_ENV === 'development' ? 200 : 50 // Higher limit for development
    },
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { sessionId } = await params;

      try {
        // Verify session ownership
        const interviewSession = await prisma.interviewSession.findFirst({
          where: {
            id: sessionId,
            userId,
          },
        });

        if (!interviewSession) {
          return NextResponse.json(
            { success: false, error: 'Interview session not found' },
            { status: 404 }
          );
        }

        // Get responses with question details
        const responses = await prisma.interviewResponse.findMany({
          where: {
            sessionId,
            userId,
          },
          include: {
            question: {
              select: {
                id: true,
                questionText: true,
                questionType: true,
                category: true,
                difficulty: true,
                expectedDuration: true,
                context: true,
                hints: true,
                questionOrder: true,
              },
            },
          },
          orderBy: {
            question: {
              questionOrder: 'asc',
            },
          },
        });

        return NextResponse.json({
          success: true,
          data: responses,
        });
      } catch (error) {
        console.error('Error fetching interview responses:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to fetch interview responses' },
          { status: 500 }
        );
      }
    }
  );
}

// POST - Submit a new response
export const POST = withSecureErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) => {
  return withRateLimit(
    request,
    {
      windowMs: 15 * 60 * 1000,
      maxRequests: process.env.NODE_ENV === 'development' ? 150 : 30 // Higher limit for development
    },
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { sessionId } = await params;

      try {
        const body = await request.json();
        const validation = submitResponseSchema.safeParse(body);
        
        if (!validation.success) {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Invalid request data',
              details: validation.error.errors 
            },
            { status: 400 }
          );
        }

        const responseData = validation.data;

        // Apply unified validation service for comprehensive validation
        const unifiedValidation = UnifiedValidationService.validateInterviewResponse({
          responseText: responseData.responseText || '',
          userNotes: responseData.userNotes,
          responseTime: responseData.responseTime,
          preparationTime: responseData.preparationTime,
        });

        if (!unifiedValidation.isValid) {
          return NextResponse.json(
            {
              success: false,
              error: 'Response validation failed',
              details: unifiedValidation.errors,
              securityFlags: unifiedValidation.securityFlags
            },
            { status: 400 }
          );
        }

        // Log security flags for monitoring
        if (unifiedValidation.securityFlags && unifiedValidation.securityFlags.length > 0) {
          console.warn('Security flags detected in response submission:', {
            userId,
            sessionId,
            questionId: responseData.questionId,
            flags: unifiedValidation.securityFlags
          });
        }

        // Use sanitized data
        const sanitizedData = {
          ...unifiedValidation.sanitizedData!,
          questionId: responseData.questionId // Add questionId from original data
        };

        // Verify session ownership and question exists
        const [interviewSession, question] = await Promise.all([
          prisma.interviewSession.findFirst({
            where: {
              id: sessionId,
              userId,
            },
          }),
          prisma.interviewQuestion.findFirst({
            where: {
              id: sanitizedData.questionId,
              sessionId,
            },
          }),
        ]);

        if (!interviewSession) {
          return NextResponse.json(
            { success: false, error: 'Interview session not found' },
            { status: 404 }
          );
        }

        if (!question) {
          return NextResponse.json(
            { success: false, error: 'Question not found' },
            { status: 404 }
          );
        }

        // Check if response already exists
        const existingResponse = await prisma.interviewResponse.findFirst({
          where: {
            userId,
            questionId: sanitizedData.questionId,
          },
        });

        if (existingResponse) {
          return NextResponse.json(
            { success: false, error: 'Response already exists for this question' },
            { status: 400 }
          );
        }

        // Create the response using sanitized data
        let createdResponse = await prisma.interviewResponse.create({
          data: {
            userId,
            sessionId,
            questionId: sanitizedData.questionId,
            responseText: sanitizedData.responseText,
            audioUrl: responseData.audioUrl, // URLs are validated by Zod schema
            videoUrl: responseData.videoUrl,
            responseTime: sanitizedData.responseTime,
            preparationTime: sanitizedData.preparationTime,
            userNotes: sanitizedData.userNotes,
            isCompleted: true,
          },
          include: {
            question: {
              select: {
                questionText: true,
                questionType: true,
                category: true,
                difficulty: true,
                context: true,
                hints: true,
              },
            },
          },
        });

        // Generate AI feedback if requested using self-healing service
        if (responseData.requestFeedback) {
          try {
            const aiResult = await SelfHealingAIService.analyzeInterviewResponse(
              question.questionText,
              sanitizedData.responseText,
              {
                timeout: 20000, // 20 seconds for response analysis
                maxRetries: 2,
                fallbackToStatic: true
              }
            );

            if (aiResult.success && aiResult.data) {
              // Validate AI score before storing using unified validation service
              const scoreValidation = UnifiedValidationService.validateAIScore(
                aiResult.data.aiScore,
                {
                  responseLength: sanitizedData.responseText.length,
                  responseTime: sanitizedData.responseTime,
                  questionType: question.questionType,
                }
              );

              if (!scoreValidation.isValid) {
                console.warn('AI score validation failed:', {
                  userId,
                  sessionId,
                  questionId: sanitizedData.questionId,
                  score: aiResult.data.aiScore,
                  errors: scoreValidation.errors,
                  flags: scoreValidation.securityFlags,
                  source: aiResult.source
                });
              }

              // Update response with AI analysis
              createdResponse = await prisma.interviewResponse.update({
                where: { id: createdResponse.id },
                data: {
                  aiScore: scoreValidation.isValid ? scoreValidation.sanitizedData : null,
                  aiAnalysis: aiResult.data.aiAnalysis,
                  feedback: aiResult.data.feedback,
                  strengths: aiResult.data.strengths,
                  improvements: aiResult.data.improvements,
                  starMethodScore: aiResult.data.starMethodScore || null,
                  confidenceLevel: aiResult.data.confidenceLevel || null,
                  communicationScore: aiResult.data.communicationScore || null,
                  technicalScore: aiResult.data.technicalScore || null,
                },
                include: {
                  question: {
                    select: {
                      questionText: true,
                      questionType: true,
                      category: true,
                      difficulty: true,
                      context: true,
                      hints: true,
                    },
                  },
                },
              });

              console.log(`AI analysis completed via ${aiResult.source} in ${aiResult.responseTime}ms (${aiResult.retryCount} retries)`);
            } else {
              console.warn('AI analysis failed:', aiResult.error);
            }
          } catch (feedbackError) {
            console.error('Error generating AI feedback:', feedbackError);
            // Continue without feedback - don't fail the response submission
          }
        }

        // Update session progress
        const completedResponses = await prisma.interviewResponse.count({
          where: {
            sessionId,
            userId,
            isCompleted: true,
          },
        });

        await prisma.interviewSession.update({
          where: { id: sessionId },
          data: {
            completedQuestions: completedResponses,
            lastActiveAt: new Date(),
          },
        });

        return NextResponse.json({
          success: true,
          data: createdResponse,
          message: 'Response submitted successfully',
        });
      } catch (error) {
        console.error('Error submitting interview response:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to submit interview response' },
          { status: 500 }
        );
      }
    }
  );
});
