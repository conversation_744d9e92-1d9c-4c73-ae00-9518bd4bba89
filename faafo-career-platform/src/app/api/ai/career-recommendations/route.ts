import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { geminiService } from '@/lib/services/geminiService';
import { cacheService, aiCacheKeys } from '@/lib/services/cacheService';
import { withErrorHandler } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema
const careerRecommendationsSchema = z.object({
  assessmentId: z.string().uuid().optional(),
  currentSkills: z.array(z.string()).min(1, 'At least one skill is required').max(50, 'Too many skills'),
  preferences: z.object({
    workEnvironment: z.enum(['remote', 'hybrid', 'office', 'flexible']).optional(),
    industryPreferences: z.array(z.string()).optional(),
    salaryExpectations: z.object({
      min: z.number().min(0).optional(),
      max: z.number().min(0).optional(),
    }).optional(),
    workLifeBalance: z.enum(['high', 'medium', 'low']).optional(),
    riskTolerance: z.enum(['high', 'medium', 'low']).optional(),
    careerStage: z.enum(['entry', 'mid', 'senior', 'executive']).optional(),
  }).optional(),
  includeAssessmentData: z.boolean().optional().default(true),
});

async function getAssessmentData(userId: string, assessmentId?: string) {
  try {
    let assessment;
    
    if (assessmentId) {
      assessment = await prisma.assessment.findFirst({
        where: {
          id: assessmentId,
          userId: userId,
          status: 'COMPLETED'
        },
        include: {
          responses: true
        }
      });
    } else {
      // Get the most recent completed assessment
      assessment = await prisma.assessment.findFirst({
        where: {
          userId: userId,
          status: 'COMPLETED'
        },
        include: {
          responses: true
        },
        orderBy: {
          completedAt: 'desc'
        }
      });
    }

    if (!assessment) {
      return null;
    }

    // Transform responses into a more usable format
    const assessmentData = {
      id: assessment.id,
      completedAt: assessment.completedAt,
      responses: assessment.responses.reduce((acc, response) => {
        acc[response.questionKey] = response.answerValue;
        return acc;
      }, {} as Record<string, any>)
    };

    return assessmentData;
  } catch (error) {
    console.error('Error fetching assessment data:', error);
    return null;
  }
}

async function handleCareerRecommendations(request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: 'Authentication required' },
      { status: 401 }
    );
  }

  const userId = session.user.id;

  try {
    const body = await request.json();
    const validation = careerRecommendationsSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid request data',
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const { assessmentId, currentSkills, preferences, includeAssessmentData } = validation.data;

    // Get assessment data if requested
    let assessmentData = null;
    if (includeAssessmentData) {
      assessmentData = await getAssessmentData(userId, assessmentId);
    }

    // Generate cache key based on input parameters
    const cacheKeyParams = [
      assessmentId || 'no-assessment',
      currentSkills.sort().join(','),
      JSON.stringify(preferences || {}),
      includeAssessmentData.toString()
    ];
    const cacheKey = aiCacheKeys.careerRecommendations(userId, cacheKeyParams.join('|'));

    // Check cache first
    const cached = await cacheService.getJSON(cacheKey);
    if (cached) {
      return NextResponse.json({
        success: true,
        data: cached,
        cached: true,
        message: 'Career recommendations retrieved from cache'
      });
    }

    // Prepare data for AI analysis
    const analysisData = {
      assessmentData: assessmentData?.responses || {},
      assessmentId: assessmentData?.id,
      assessmentCompletedAt: assessmentData?.completedAt,
      currentSkills,
      preferences: preferences || {},
      userId // For context but not for AI processing
    };

    // Generate AI recommendations
    const recommendationsResult = await geminiService.generateCareerRecommendations(
      analysisData.assessmentData,
      currentSkills,
      preferences || {},
      userId
    );

    if (!recommendationsResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: recommendationsResult.error || 'Failed to generate career recommendations' 
        },
        { status: 500 }
      );
    }

    // Enhance recommendations with additional data
    const enhancedData = {
      ...recommendationsResult.data,
      metadata: {
        generatedAt: new Date().toISOString(),
        basedOnAssessment: !!assessmentData,
        assessmentId: assessmentData?.id,
        skillsCount: currentSkills.length,
        hasPreferences: !!preferences && Object.keys(preferences).length > 0
      }
    };

    // Cache the result for 6 hours (recommendations can change based on market conditions)
    await cacheService.setJSON(cacheKey, enhancedData, 6 * 60 * 60);

    // Track usage analytics
    console.log(`Career recommendations generated for user ${userId}, assessment: ${assessmentId || 'none'}`);

    return NextResponse.json({
      success: true,
      data: enhancedData,
      cached: false,
      message: 'Career recommendations generated successfully'
    });

  } catch (error) {
    console.error('Career recommendations error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred while generating recommendations' 
      },
      { status: 500 }
    );
  }
}

// GET endpoint for retrieving user's recent recommendations
export async function GET(request: NextRequest) {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 30 }, // 30 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { searchParams } = new URL(request.url);
      const assessmentId = searchParams.get('assessmentId');

      try {
        // Get user's recent assessment if no specific ID provided
        let targetAssessmentId = assessmentId;
        if (!targetAssessmentId) {
          const recentAssessment = await prisma.assessment.findFirst({
            where: {
              userId: userId,
              status: 'COMPLETED'
            },
            orderBy: {
              completedAt: 'desc'
            },
            select: {
              id: true
            }
          });
          targetAssessmentId = recentAssessment?.id || 'no-assessment';
        }

        // Try to find cached recommendations
        // Note: This is a simplified approach - in production you might want to store cache keys
        const possibleCacheKey = aiCacheKeys.careerRecommendations(userId, targetAssessmentId);
        const cached = await cacheService.getJSON(possibleCacheKey);

        if (cached) {
          return NextResponse.json({
            success: true,
            data: cached,
            cached: true
          });
        }

        return NextResponse.json({
          success: false,
          error: 'No cached recommendations found. Please generate new recommendations.',
          code: 'NO_CACHE'
        }, { status: 404 });

      } catch (error) {
        console.error('Error retrieving recommendations:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to retrieve recommendations' },
          { status: 500 }
        );
      }
    }
  );
}

// POST endpoint for generating new recommendations
export const POST = withErrorHandler(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 generations per 15 minutes
      () => handleCareerRecommendations(request)
    );
  });
});

// DELETE endpoint for clearing cached recommendations
export async function DELETE(request: NextRequest) {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 15 }, // 15 deletions per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { searchParams } = new URL(request.url);
      const assessmentId = searchParams.get('assessmentId');

      try {
        // Clear specific or all recommendations cache for user
        if (assessmentId) {
          const cacheKey = aiCacheKeys.careerRecommendations(userId, assessmentId);
          await cacheService.delete(cacheKey);
        } else {
          // Clear all recommendation caches for user (this would require pattern matching in Redis)
          console.log(`Clearing all recommendation caches for user ${userId}`);
          // For now, we'll just log this - implementing pattern deletion would require Redis SCAN
        }

        return NextResponse.json({
          success: true,
          message: 'Recommendation cache cleared'
        });

      } catch (error) {
        console.error('Error clearing recommendation cache:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to clear cache' },
          { status: 500 }
        );
      }
    }
  );
}
