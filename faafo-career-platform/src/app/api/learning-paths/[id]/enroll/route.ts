import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { withError<PERSON>andler } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { cacheService } from '@/lib/services/cacheService';
import { withCSRFProtection } from '@/lib/csrf';

// POST - Enroll in learning path
export const POST = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 50 }, // 50 enrollments per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;

      try {
        const { id: learningPathId } = await params;

        // Check if learning path exists and is active
        const learningPath = await prisma.learningPath.findUnique({
          where: { id: learningPathId },
          include: {
            steps: {
              orderBy: { stepOrder: 'asc' },
              select: {
                id: true,
                stepOrder: true,
                isRequired: true,
              }
            }
          }
        });

        if (!learningPath) {
          return NextResponse.json(
            { success: false, error: 'Learning path not found' },
            { status: 404 }
          );
        }

        if (!learningPath.isActive) {
          return NextResponse.json(
            { success: false, error: 'Learning path is not available for enrollment' },
            { status: 400 }
          );
        }

        // Check if user is already enrolled
        const existingEnrollment = await prisma.userLearningPath.findUnique({
          where: {
            userId_learningPathId: {
              userId,
              learningPathId
            }
          }
        });

        if (existingEnrollment) {
          return NextResponse.json(
            { success: false, error: 'Already enrolled in this learning path' },
            { status: 409 }
          );
        }

        // Create enrollment
        const enrollment = await prisma.userLearningPath.create({
          data: {
            userId,
            learningPathId,
            status: 'NOT_STARTED',
            totalSteps: learningPath.steps.length,
            currentStepId: learningPath.steps[0]?.id,
          },
          include: {
            learningPath: {
              select: {
                id: true,
                title: true,
                description: true,
                difficulty: true,
                estimatedHours: true,
                category: true,
              }
            }
          }
        });

        // Create progress records for all steps
        if (learningPath.steps.length > 0) {
          const stepProgressData = learningPath.steps.map(step => ({
            userId,
            userLearningPathId: enrollment.id,
            stepId: step.id,
            status: 'NOT_STARTED' as const,
          }));

          await prisma.userLearningPathProgress.createMany({
            data: stepProgressData
          });
        }

        // Create initial learning analytics record
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        await prisma.learningAnalytics.upsert({
          where: {
            userId_date: {
              userId,
              date: today
            }
          },
          update: {
            pathsProgressed: {
              increment: 1
            }
          },
          create: {
            userId,
            date: today,
            pathsProgressed: 1,
          }
        });

        // Clear relevant caches
        await cacheService.delete(`learning_path:${learningPathId}:${userId}`);
        await cacheService.delete(`user_learning_paths:${userId}:*`);

        return NextResponse.json({
          success: true,
          data: enrollment,
          message: 'Successfully enrolled in learning path'
        }, { status: 201 });

      } catch (error) {
        console.error('Error enrolling in learning path:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to enroll in learning path' },
          { status: 500 }
        );
      }
    }
  );
});

// DELETE - Unenroll from learning path
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 unenrollments per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;

      try {
        const { id: learningPathId } = await params;

        // Check if user is enrolled
        const enrollment = await prisma.userLearningPath.findUnique({
          where: {
            userId_learningPathId: {
              userId,
              learningPathId
            }
          }
        });

        if (!enrollment) {
          return NextResponse.json(
            { success: false, error: 'Not enrolled in this learning path' },
            { status: 404 }
          );
        }

        // Check if path is completed
        if (enrollment.status === 'COMPLETED') {
          return NextResponse.json(
            { success: false, error: 'Cannot unenroll from completed learning path' },
            { status: 400 }
          );
        }

        // Delete enrollment and all related progress
        await prisma.userLearningPath.delete({
          where: {
            userId_learningPathId: {
              userId,
              learningPathId
            }
          }
        });

        // Clear relevant caches
        await cacheService.delete(`learning_path:${learningPathId}:${userId}`);
        await cacheService.delete(`user_learning_paths:${userId}:*`);

        return NextResponse.json({
          success: true,
          message: 'Successfully unenrolled from learning path'
        });

      } catch (error) {
        console.error('Error unenrolling from learning path:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to unenroll from learning path' },
          { status: 500 }
        );
      }
    }
  );
}

// GET - Get enrollment status and progress
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 100 }, // 100 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;

      try {
        const { id: learningPathId } = await params;

        // Build cache key
        const cacheKey = `enrollment:${learningPathId}:${userId}`;
        
        // Check cache first
        const cached = await cacheService.getJSON(cacheKey);
        if (cached) {
          return NextResponse.json({
            success: true,
            data: cached,
            cached: true
          });
        }

        // Get enrollment with detailed progress
        const enrollment = await prisma.userLearningPath.findUnique({
          where: {
            userId_learningPathId: {
              userId,
              learningPathId
            }
          },
          include: {
            learningPath: {
              select: {
                id: true,
                title: true,
                description: true,
                difficulty: true,
                estimatedHours: true,
                category: true,
              }
            },
            stepProgress: {
              include: {
                step: {
                  select: {
                    id: true,
                    title: true,
                    stepOrder: true,
                    stepType: true,
                    estimatedMinutes: true,
                    isRequired: true,
                  }
                }
              },
              orderBy: {
                step: {
                  stepOrder: 'asc'
                }
              }
            }
          }
        });

        if (!enrollment) {
          return NextResponse.json({
            success: true,
            data: {
              enrolled: false,
              enrollment: null
            }
          });
        }

        // Calculate detailed progress metrics
        const completedSteps = enrollment.stepProgress.filter(p => p.status === 'COMPLETED').length;
        const inProgressSteps = enrollment.stepProgress.filter(p => p.status === 'IN_PROGRESS').length;
        const totalTimeSpent = enrollment.stepProgress.reduce((sum, p) => sum + p.timeSpent, 0);
        
        const progressData = {
          enrolled: true,
          enrollment: {
            ...enrollment,
            progressMetrics: {
              completedSteps,
              inProgressSteps,
              totalSteps: enrollment.totalSteps,
              progressPercent: enrollment.progressPercent,
              totalTimeSpent,
              averageStepTime: completedSteps > 0 ? Math.round(totalTimeSpent / completedSteps) : 0,
            },
            nextStep: enrollment.stepProgress.find(p => p.status === 'NOT_STARTED')?.step || null,
            currentStep: enrollment.currentStepId ? 
              enrollment.stepProgress.find(p => p.stepId === enrollment.currentStepId)?.step || null : null,
          }
        };

        // Cache for 2 minutes
        await cacheService.setJSON(cacheKey, progressData, 2 * 60);

        return NextResponse.json({
          success: true,
          data: progressData
        });

      } catch (error) {
        console.error('Error fetching enrollment status:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to fetch enrollment status' },
          { status: 500 }
        );
      }
    }
  );
}
