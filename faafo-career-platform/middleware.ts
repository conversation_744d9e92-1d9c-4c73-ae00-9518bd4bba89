import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/profile',
  '/assessment',
  '/forum',
  '/freedom-fund',
  '/progress',
  '/recommendations',
  '/interview-practice',
  '/resume-builder',
  '/tools',
];

// Define API routes that require authentication
const protectedApiRoutes = [
  '/api/assessment',
  '/api/profile',
  '/api/freedom-fund',
  '/api/learning-progress',
  '/api/personalized-resources',
  '/api/progress-tracker',
  '/api/recommendations',
  '/api/resource-ratings',
  '/api/interview-practice',
  '/api/resume-builder',
  '/api/tools',
];

// Define public API routes that don't require authentication
const publicApiRoutes = [
  '/api/auth',
  '/api/signup',
  '/api/career-paths',
  '/api/learning-resources',
  '/api/contact',
  '/api/csrf-token',
];

// Rate limiting store (use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  const realIP = request.headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }
  
  return (request as any).ip || 'unknown';
}

function isRateLimited(request: NextRequest, windowMs: number = 15 * 60 * 1000, maxRequests: number = 100): boolean {
  const clientIP = getClientIP(request);
  const now = Date.now();
  const windowStart = now - windowMs;
  
  // Clean up old entries
  Array.from(rateLimitStore.entries()).forEach(([key, value]) => {
    if (value.resetTime < windowStart) {
      rateLimitStore.delete(key);
    }
  });
  
  // Get or create entry for this IP
  const entry = rateLimitStore.get(clientIP) || { count: 0, resetTime: now + windowMs };
  
  // Reset if window has expired
  if (entry.resetTime < now) {
    entry.count = 0;
    entry.resetTime = now + windowMs;
  }
  
  // Increment count
  entry.count++;
  rateLimitStore.set(clientIP, entry);
  
  return entry.count > maxRequests;
}

function addSecurityHeaders(response: NextResponse): NextResponse {
  // Add comprehensive security headers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  // Generate nonce for inline scripts
  const nonce = crypto.randomUUID();

  // Enhanced Content Security Policy - Removed unsafe-inline for better security
  const csp = [
    "default-src 'self'",
    `script-src 'self' 'nonce-${nonce}' 'unsafe-eval' https://vercel.live https://va.vercel-scripts.com https://browser.sentry-cdn.com`,
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "img-src 'self' data: https: blob:",
    "font-src 'self' data: https://fonts.gstatic.com",
    "connect-src 'self' https://vercel.live https://vitals.vercel-insights.com https://*.sentry.io",
    "worker-src 'self' blob:",
    "child-src 'self' blob:",
    "frame-ancestors 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ');

  response.headers.set('Content-Security-Policy', csp);
  response.headers.set('X-Nonce', nonce);

  // Add HSTS header for HTTPS (always set for security testing)
  response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');

  // Additional security headers
  response.headers.set('Cross-Origin-Embedder-Policy', 'require-corp');
  response.headers.set('Cross-Origin-Opener-Policy', 'same-origin');
  response.headers.set('Cross-Origin-Resource-Policy', 'same-origin');

  return response;
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api/_next') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next();
  }

  // Apply rate limiting for API routes
  if (pathname.startsWith('/api/')) {
    if (isRateLimited(request)) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      );
    }
  }

  // Get the token to check authentication status
  const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });

  // Check if the route requires authentication
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route));
  const isProtectedApiRoute = protectedApiRoutes.some(route => pathname.startsWith(route));
  const isPublicApiRoute = publicApiRoutes.some(route => pathname.startsWith(route));

  // Handle protected routes
  if (isProtectedRoute && !token) {
    // Prevent redirect loops by checking if we're already being redirected
    const isRedirectLoop = request.headers.get('x-middleware-redirect-count');
    if (isRedirectLoop && parseInt(isRedirectLoop) > 3) {
      console.error('Redirect loop detected, breaking chain');
      return NextResponse.json(
        { error: 'Authentication redirect loop detected' },
        { status: 500 }
      );
    }

    const loginUrl = new URL('/login', request.url);
    // Include query parameters in the redirect URL
    const fullPath = pathname + (request.nextUrl.search || '');
    loginUrl.searchParams.set('callbackUrl', fullPath);

    const response = NextResponse.redirect(loginUrl);
    // Track redirect count to prevent loops
    const redirectCount = parseInt(isRedirectLoop || '0') + 1;
    response.headers.set('x-middleware-redirect-count', redirectCount.toString());
    return response;
  }

  // Handle protected API routes
  if (isProtectedApiRoute && !token && !isPublicApiRoute) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    );
  }

  // Redirect authenticated users away from auth pages (with loop prevention)
  if (token && (pathname === '/login' || pathname === '/signup')) {
    // Check for redirect loops
    const isRedirectLoop = request.headers.get('x-middleware-redirect-count');
    if (isRedirectLoop && parseInt(isRedirectLoop) > 3) {
      console.error('Auth redirect loop detected, allowing access to auth page');
      // Allow access to prevent infinite loops
      const response = NextResponse.next();
      return addSecurityHeaders(response);
    }

    // Check if user is trying to access login with a callback URL
    const callbackUrl = request.nextUrl.searchParams.get('callbackUrl');
    const redirectTo = callbackUrl && callbackUrl !== '/login' && callbackUrl !== '/signup'
      ? callbackUrl
      : '/dashboard';

    const response = NextResponse.redirect(new URL(redirectTo, request.url));
    // Track redirect count
    const redirectCount = parseInt(isRedirectLoop || '0') + 1;
    response.headers.set('x-middleware-redirect-count', redirectCount.toString());
    return response;
  }

  const response = NextResponse.next();
  return addSecurityHeaders(response);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
