{"name": "faafo-career-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:real": "jest --config jest.config.simple.js __tests__/real-database.test.ts", "test:setup": "tsx scripts/setup-test-database.ts", "test:verify": "tsx scripts/verify-real-database-testing.ts", "test:all": "./run-tests.sh", "test:edge-cases": "tsx __tests__/edge-cases/run-edge-case-tests.ts", "test:edge-cases:auth": "npx jest __tests__/edge-cases/auth-security.test.ts", "test:edge-cases:api": "npx jest __tests__/edge-cases/api-validation.test.ts", "test:edge-cases:db": "npx jest __tests__/edge-cases/database-operations.test.ts", "test:edge-cases:errors": "npx jest __tests__/edge-cases/error-handling.test.ts", "test:edge-cases:performance": "npx jest __tests__/edge-cases/performance-security.test.ts", "test:edge-cases:validation": "npx jest __tests__/edge-cases/validation-edge-cases.test.ts", "test:coverage": "npx jest --coverage", "test:watch": "npx jest --watch", "test:ci": "npx jest --ci --coverage --watchAll=false", "test:ai-service": "tsx __tests__/ai-service/run-ai-tests.ts", "test:ai-service:core": "npx jest __tests__/ai-service/gemini-service.test.ts", "test:ai-service:security": "npx jest __tests__/ai-service/security-validation.test.ts", "test:ai-service:cache": "npx jest __tests__/ai-service/redis-cache.test.ts", "test:ai-service:monitoring": "npx jest __tests__/ai-service/monitoring.test.ts", "test:ai-service:integration": "npx jest __tests__/ai-service/integration.test.ts", "test:security": "jest __tests__/security/skill-gap-security.test.js", "test:performance": "jest __tests__/performance/skill-gap-performance.test.js", "test:load": "node scripts/performance/load-test-skill-gap.js", "test:performance:monitor": "node scripts/performance/performance-monitor.js", "test:performance:validate": "node scripts/performance/validate-performance.js", "test:security:audit": "node scripts/security/security-audit.js", "test:security:pentest": "node scripts/security/penetration-test.js", "test:security:full": "npm run test:security:audit && npm run test:security:pentest && npm run test:security", "test:monitoring": "jest __tests__/monitoring/monitoring-system.test.ts", "test-crud": "tsx scripts/test-prisma-crud.ts", "prisma:seed": "tsx prisma/seed.ts", "postinstall": "npx prisma generate", "deploy:validate": "node scripts/validate-production-env.js", "deploy:vercel": "./scripts/deploy-to-vercel.sh", "deploy:preview": "vercel", "deploy:production": "vercel --prod", "build:production": "NODE_ENV=production npm run build", "db:migrate:deploy": "npx prisma migrate deploy", "db:generate": "npx prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@google/generative-ai": "^0.21.0", "@hookform/resolvers": "^5.1.1", "@opentelemetry/api": "^1.9.0", "@prisma/client": "^6.8.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.0.41", "@react-email/html": "^0.0.11", "@sentry/nextjs": "^8.47.0", "@types/bcryptjs": "^2.4.6", "@types/dompurify": "^3.0.5", "@types/jsdom": "^21.1.7", "@types/node-fetch": "^2.6.12", "@vercel/analytics": "^1.5.0", "@vercel/blob": "^1.1.1", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.4", "fuse.js": "^7.1.0", "helmet": "^8.1.0", "ioredis": "^5.4.1", "isomorphic-dompurify": "^2.25.0", "js-yaml": "^4.1.0", "jsdom": "^26.1.0", "lru-cache": "^11.1.0", "lucide-react": "^0.511.0", "mammoth": "^1.8.0", "next": "^14.2.5", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-fetch": "2", "nodemailer": "^6.10.1", "pdf-parse": "^1.1.1", "playwright": "^1.53.1", "prisma": "^6.8.2", "prismjs": "^1.30.0", "react": "^18.2.0", "react-cookie-consent": "^9.0.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "recharts": "^2.15.3", "resend": "^4.5.1", "sonner": "^2.0.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-react": "^5.24.2", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.25.56"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcrypt": "^5.0.2", "@types/eslint-plugin-security": "^3.0.0", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/lru-cache": "^7.10.9", "@types/node": "^20", "@types/pdf-parse": "^1.1.4", "@types/react": "^19", "@types/react-dom": "^19", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-react": "^5.0.1", "@types/uuid": "^10.0.0", "babel-jest": "^30.0.0-beta.3", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.3.3", "eslint-plugin-security": "^3.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.4", "tw-animate-css": "^1.3.2", "typescript": "^5"}}