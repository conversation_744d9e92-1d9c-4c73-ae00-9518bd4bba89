#!/bin/bash

# 📚 FAAFO Documentation Structure Validator
# This script ensures all documentation follows the organization system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}📚 FAAFO Documentation Structure Validator${NC}"
echo "=================================================="

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Function to print warning
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to print info
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Initialize counters
ERRORS=0
WARNINGS=0

echo -e "\n${BLUE}🔍 Checking for scattered documentation...${NC}"

# Check for .md files outside docs/ (excluding README.md files, backup directories, and test artifacts)
SCATTERED_DOCS=$(find . -name "*.md" -not -path "./docs/*" -not -path "./node_modules/*" -not -path "./.next/*" -not -path "./backups/*" -not -path "./faafo-career-platform/node_modules/*" -not -path "./faafo-career-platform/coverage/*" -not -path "*/playwright-report/*" -not -path "*/test-results/*" -not -name "README.md" 2>/dev/null || true)

if [ -n "$SCATTERED_DOCS" ]; then
    echo -e "${RED}❌ Found documentation files outside docs/ directory:${NC}"
    echo "$SCATTERED_DOCS" | while read -r file; do
        echo -e "   ${RED}• $file${NC}"
    done
    ERRORS=$((ERRORS + 1))
    echo ""
    echo -e "${YELLOW}💡 Please move these files to the appropriate docs/ subdirectory:${NC}"
    echo -e "   • Development docs → docs/development/"
    echo -e "   • Feature docs → docs/features/"
    echo -e "   • Testing docs → docs/testing/"
    echo -e "   • User guides → docs/user-guides/"
    echo -e "   • Operations → docs/operations/"
    echo -e "   • Project management → docs/project-management/"
else
    print_status 0 "No scattered documentation found"
fi

echo -e "\n${BLUE}🏗️  Checking docs/ directory structure...${NC}"

# Check if main docs directory exists
if [ ! -d "docs" ]; then
    echo -e "${RED}❌ Main docs/ directory not found!${NC}"
    ERRORS=$((ERRORS + 1))
else
    print_status 0 "Main docs/ directory exists"
    
    # Check for required subdirectories
    REQUIRED_DIRS=("development" "features" "operations" "testing" "user-guides" "project-management")
    
    for dir in "${REQUIRED_DIRS[@]}"; do
        if [ ! -d "docs/$dir" ]; then
            print_warning "Missing docs/$dir/ directory"
            WARNINGS=$((WARNINGS + 1))
        else
            print_status 0 "docs/$dir/ directory exists"
        fi
    done
    
    # Check for README files in subdirectories
    for dir in "${REQUIRED_DIRS[@]}"; do
        if [ -d "docs/$dir" ] && [ ! -f "docs/$dir/README.md" ]; then
            print_warning "Missing README.md in docs/$dir/"
            WARNINGS=$((WARNINGS + 1))
        fi
    done
fi

echo -e "\n${BLUE}📋 Checking documentation organization...${NC}"

# Count docs in each category
if [ -d "docs" ]; then
    TOTAL_DOCS=$(find docs/ -name "*.md" | wc -l)
    print_info "Total documentation files: $TOTAL_DOCS"
    
    for dir in development features operations testing user-guides project-management; do
        if [ -d "docs/$dir" ]; then
            COUNT=$(find "docs/$dir" -name "*.md" | wc -l)
            print_info "docs/$dir/: $COUNT files"
        fi
    done
fi

echo -e "\n${BLUE}🔍 Checking for duplicate docs directories...${NC}"

# Check for duplicate docs directories (excluding node_modules and coverage)
DUPLICATE_DOCS=$(find . -type d -name "docs" -not -path "./docs" -not -path "./node_modules/*" -not -path "./.next/*" -not -path "./faafo-career-platform/node_modules/*" -not -path "./faafo-career-platform/coverage/*" -not -path "./backups/*" 2>/dev/null || true)

if [ -n "$DUPLICATE_DOCS" ]; then
    echo -e "${RED}❌ Found duplicate docs directories:${NC}"
    echo "$DUPLICATE_DOCS" | while read -r dir; do
        echo -e "   ${RED}• $dir${NC}"
    done
    ERRORS=$((ERRORS + 1))
    echo -e "${YELLOW}💡 Please consolidate all documentation into the main docs/ directory${NC}"
else
    print_status 0 "No duplicate docs directories found"
fi

echo -e "\n${BLUE}📝 Checking file naming conventions...${NC}"

# Check for files that might be misnamed
if [ -d "docs" ]; then
    # Look for files that might need better organization
    LOOSE_FILES=$(find docs/ -maxdepth 1 -name "*.md" -not -name "README.md" -not -name "PROJECT_*.md" -not -name "DOCUMENTATION_*.md" -not -name "UNIVERSAL_*.md" 2>/dev/null || true)
    
    if [ -n "$LOOSE_FILES" ]; then
        print_warning "Found documentation files in docs/ root that might belong in subdirectories:"
        echo "$LOOSE_FILES" | while read -r file; do
            echo -e "   ${YELLOW}• $file${NC}"
        done
        WARNINGS=$((WARNINGS + 1))
    else
        print_status 0 "Documentation files are properly organized"
    fi
fi

# Summary
echo -e "\n${BLUE}📊 Validation Summary${NC}"
echo "===================="

if [ $ERRORS -eq 0 ] && [ $WARNINGS -eq 0 ]; then
    echo -e "${GREEN}🎉 Perfect! Documentation structure is well-organized.${NC}"
    exit 0
elif [ $ERRORS -eq 0 ]; then
    echo -e "${YELLOW}⚠️  Documentation structure is mostly good with $WARNINGS warnings.${NC}"
    exit 0
else
    echo -e "${RED}❌ Found $ERRORS errors and $WARNINGS warnings in documentation structure.${NC}"
    echo -e "\n${BLUE}🔧 To fix these issues:${NC}"
    echo "1. Move scattered .md files to appropriate docs/ subdirectories"
    echo "2. Remove duplicate docs directories"
    echo "3. Follow the naming conventions in DOCUMENTATION_ORGANIZATION_SYSTEM.md"
    echo ""
    echo -e "${BLUE}📖 For detailed guidance, see: DOCUMENTATION_ORGANIZATION_SYSTEM.md${NC}"
    exit 1
fi
